# -*- coding: utf-8 -*-

"""
简化测试：验证新的眨眼检测逻辑
"""

def test_blink_logic():
    """
    模拟眨眼检测逻辑的测试
    """
    print("=== 测试新的眨眼检测逻辑 ===")
    
    # 模拟旋转差异数据（度）
    # 帧号: 旋转差异
    rotation_data = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 25,  # 超过阈值 - 应该输出0（眨眼开始）
        4: 30,  # 仍然超过阈值 - 不输出
        5: 35,  # 仍然超过阈值 - 不输出
        6: 20,  # 仍然超过阈值 - 不输出
        7: 10,  # 恢复到阈值内 - 应该输出1（眨眼结束）
        8: 5,   # 未超过阈值
        9: 25,  # 超过阈值但在冷却期内 - 不应该输出
        10: 30, # 超过阈值但在冷却期内 - 不应该输出
        11: 8,  # 冷却期内 - 不应该输出
        12: 5,  # 冷却期结束
        13: 28, # 超过阈值，冷却期结束 - 应该输出0（第二次眨眼开始）
        14: 35, # 仍然超过阈值 - 不输出
        15: 12, # 恢复到阈值内 - 应该输出1（第二次眨眼结束）
    }
    
    # 参数设置
    frame_spacing = 2  # n=2
    angle_threshold = 15  # 15度阈值
    cooldown_frames = 2 * frame_spacing  # 冷却期：4帧
    
    print(f"参数设置:")
    print(f"- 帧间隔(n): {frame_spacing}")
    print(f"- 角度阈值: {angle_threshold}度")
    print(f"- 冷却期: {cooldown_frames}帧")
    print()
    
    # 状态跟踪
    is_exceeding_threshold = False
    last_end_frame = None
    result_markers = {}
    
    print("逐帧分析:")
    for frame in sorted(rotation_data.keys()):
        rotation_diff = rotation_data[frame]
        
        # 检查是否在冷却期内
        if last_end_frame is not None and frame - last_end_frame < cooldown_frames:
            print(f"帧 {frame}: 旋转差异 = {rotation_diff:.1f}度 -> 在冷却期内，跳过检测")
            continue
        
        # 判断当前是否超过阈值
        current_exceeds = rotation_diff > angle_threshold
        
        if current_exceeds and not is_exceeding_threshold:
            # 首次超过阈值，输出0（眨眼开始）
            result_markers[frame] = 0
            is_exceeding_threshold = True
            print(f"帧 {frame}: 旋转差异 = {rotation_diff:.1f}度 -> 眨眼开始! 标记0")
        elif not current_exceeds and is_exceeding_threshold:
            # 从超过阈值恢复到阈值内，输出1（眨眼结束）
            result_markers[frame] = 1
            is_exceeding_threshold = False
            last_end_frame = frame  # 记录眨眼结束帧，开始冷却期
            print(f"帧 {frame}: 旋转差异 = {rotation_diff:.1f}度 -> 眨眼结束! 标记1，开始{cooldown_frames}帧冷却期")
        elif current_exceeds and is_exceeding_threshold:
            # 仍然超过阈值，不输出（眨眼进行中）
            print(f"帧 {frame}: 旋转差异 = {rotation_diff:.1f}度 -> 眨眼进行中，不输出标记")
        else:
            # 未超过阈值且之前也未超过，不输出
            print(f"帧 {frame}: 旋转差异 = {rotation_diff:.1f}度 -> 未超过阈值")
    
    print(f"\n=== 最终结果 ===")
    print(f"检测到的标记: {len(result_markers)}")
    if result_markers:
        print("标记结果:")
        for frame in sorted(result_markers.keys()):
            marker_type = "眨眼开始" if result_markers[frame] == 0 else "眨眼结束"
            print(f"  帧 {frame}: {result_markers[frame]} ({marker_type})")
    else:
        print("未检测到眨眼")
    
    print(f"\n=== 预期结果验证 ===")
    expected_results = {3: 0, 7: 1, 13: 0, 15: 1}
    print("预期结果:", expected_results)
    
    if result_markers == expected_results:
        print("✅ 测试通过！逻辑正确")
    else:
        print("❌ 测试失败！逻辑需要调整")
        print(f"实际结果: {result_markers}")
        print(f"预期结果: {expected_results}")
    
    return result_markers


if __name__ == "__main__":
    test_blink_logic()
