#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线K帧编辑器UI
"""

import sys
import math
import copy
from Qt import QtWidgets, QtCore, QtGui


class BezierPoint:
    """贝塞尔曲线关键点"""
    
    def __init__(self, time=0.0, value=0.0, in_tangent=(0, 0), out_tangent=(0, 0)):
        self.time = time
        self.value = value
        self.in_tangent = in_tangent    # 入切线
        self.out_tangent = out_tangent  # 出切线
        self.selected = False


class BezierCurveWidget(QtWidgets.QWidget):
    """贝塞尔曲线编辑器控件"""
    
    keyframe_changed = QtCore.Signal(list)  # 关键帧改变信号
    
    def __init__(self, parent=None):
        super(BezierCurveWidget, self).__init__(parent)
        self.setMinimumSize(400, 300)
        self.setMouseTracking(True)

        # 设置焦点策略，使控件能够接收键盘事件
        self.setFocusPolicy(QtCore.Qt.StrongFocus)

        # 曲线数据
        self.keyframes = []
        self.selected_keyframe = None
        self.dragging_keyframe = None
        self.dragging_tangent = None
        self.drag_start_pos = None

        # 撤销/重做系统
        self.undo_stack = []
        self.redo_stack = []
        self.max_undo_steps = 50

        # 视图参数
        self.time_range = (0.0, 10.0)
        self.value_range = (-1.0, 1.0)
        self.grid_enabled = True

        # 绘制参数
        self.keyframe_size = 8
        self.tangent_size = 6

        # 初始化一些默认关键帧
        self.add_keyframe(0.0, 0.0, save_state=False)
        self.add_keyframe(5.0, 1.0, save_state=False)
        self.add_keyframe(10.0, 0.0, save_state=False)

        # 保存初始状态
        self.save_state("初始状态")

    def save_state(self, action_name="操作"):
        """保存当前状态到撤销栈"""
        # 深拷贝当前关键帧状态
        state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': action_name
        }

        self.undo_stack.append(state)

        # 限制撤销栈大小
        if len(self.undo_stack) > self.max_undo_steps:
            self.undo_stack.pop(0)

        # 清空重做栈
        self.redo_stack.clear()

    def get_keyframe_index(self, keyframe):
        """获取关键帧在列表中的索引"""
        if keyframe and keyframe in self.keyframes:
            return self.keyframes.index(keyframe)
        return -1

    def undo(self):
        """撤销操作"""
        if len(self.undo_stack) <= 1:  # 保留初始状态
            print("没有可撤销的操作")
            return False

        # 保存当前状态到重做栈
        current_state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': "当前状态"
        }
        self.redo_stack.append(current_state)

        # 恢复上一个状态
        self.undo_stack.pop()  # 移除当前状态
        previous_state = self.undo_stack[-1]  # 获取上一个状态

        self.restore_state(previous_state)
        print(f"撤销: {previous_state['action_name']}")
        return True

    def redo(self):
        """重做操作"""
        if not self.redo_stack:
            print("没有可重做的操作")
            return False

        # 保存当前状态到撤销栈
        current_state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': "撤销前状态"
        }
        self.undo_stack.append(current_state)

        # 恢复重做状态
        redo_state = self.redo_stack.pop()
        self.restore_state(redo_state)
        print(f"重做: {redo_state['action_name']}")
        return True

    def restore_state(self, state):
        """恢复状态"""
        self.keyframes = copy.deepcopy(state['keyframes'])

        # 恢复选中的关键帧
        selected_index = state['selected_keyframe_index']
        if 0 <= selected_index < len(self.keyframes):
            self.selected_keyframe = self.keyframes[selected_index]
        else:
            self.selected_keyframe = None

        self.update()
        self.keyframe_changed.emit(self.keyframes)
    
    def add_keyframe(self, time, value, save_state=True):
        """添加关键帧"""
        if save_state:
            self.save_state(f"添加关键帧 ({time:.3f}, {value:.3f})")

        # 计算默认切线
        in_tangent = (-1.0, 0.0)
        out_tangent = (1.0, 0.0)

        keyframe = BezierPoint(time, value, in_tangent, out_tangent)
        self.keyframes.append(keyframe)
        self.keyframes.sort(key=lambda k: k.time)
        self.update()
        self.keyframe_changed.emit(self.keyframes)
        return keyframe  # 返回新创建的关键帧
    
    def remove_keyframe(self, keyframe, save_state=True):
        """删除关键帧"""
        if keyframe in self.keyframes and len(self.keyframes) > 2:
            if save_state:
                self.save_state(f"删除关键帧 ({keyframe.time:.3f}, {keyframe.value:.3f})")

            self.keyframes.remove(keyframe)
            if self.selected_keyframe == keyframe:
                self.selected_keyframe = None
            self.update()
            self.keyframe_changed.emit(self.keyframes)
    
    def world_to_screen(self, time, value):
        """世界坐标转屏幕坐标"""
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        time_norm = (time - self.time_range[0]) / (self.time_range[1] - self.time_range[0])
        value_norm = (value - self.value_range[0]) / (self.value_range[1] - self.value_range[0])
        
        x = rect.left() + time_norm * rect.width()
        y = rect.bottom() - value_norm * rect.height()
        
        return QtCore.QPointF(x, y)
    
    def screen_to_world(self, screen_pos):
        """屏幕坐标转世界坐标"""
        # 确保screen_pos是QPointF类型
        if isinstance(screen_pos, QtCore.QPoint):
            screen_pos = QtCore.QPointF(screen_pos)

        rect = self.rect().adjusted(20, 20, -20, -20)

        time_norm = (screen_pos.x() - rect.left()) / rect.width()
        value_norm = (rect.bottom() - screen_pos.y()) / rect.height()

        time = self.time_range[0] + time_norm * (self.time_range[1] - self.time_range[0])
        value = self.value_range[0] + value_norm * (self.value_range[1] - self.value_range[0])

        return time, value

    def is_pos_in_curve_area(self, pos):
        """检查位置是否在曲线编辑区域内"""
        # 确保pos是QPointF类型
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)

        # 获取曲线编辑区域（去掉边距）
        curve_rect = self.rect().adjusted(20, 20, -20, -20)

        return curve_rect.contains(pos.toPoint())
    
    def get_keyframe_at_pos(self, pos):
        """获取指定位置的关键帧"""
        # 确保pos是QPointF类型
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)

        for keyframe in self.keyframes:
            screen_pos = self.world_to_screen(keyframe.time, keyframe.value)
            if (pos - screen_pos).manhattanLength() < self.keyframe_size:
                return keyframe
        return None
    
    def get_tangent_at_pos(self, pos):
        """获取指定位置的切线控制点"""
        # 确保pos是QPointF类型
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)

        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)

            # 计算切线长度比例 - 根据相邻关键帧距离调整
            segment_length = 30  # 默认长度
            
            # 如果有前一个关键帧，调整入切线长度
            prev_keyframe = None
            for kf in self.keyframes:
                if kf.time < keyframe.time and (prev_keyframe is None or kf.time > prev_keyframe.time):
                    prev_keyframe = kf
            
            if prev_keyframe:
                in_segment_length = (keyframe.time - prev_keyframe.time) * 30
            else:
                in_segment_length = segment_length
                
            # 如果有后一个关键帧，调整出切线长度
            next_keyframe = None
            for kf in self.keyframes:
                if kf.time > keyframe.time and (next_keyframe is None or kf.time < next_keyframe.time):
                    next_keyframe = kf
                    
            if next_keyframe:
                out_segment_length = (next_keyframe.time - keyframe.time) * 30
            else:
                out_segment_length = segment_length

            # 检查入切线
            in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * in_segment_length, 
                                           -keyframe.in_tangent[1] * in_segment_length)
            if (pos - in_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'in'

            # 检查出切线
            out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * out_segment_length, 
                                           -keyframe.out_tangent[1] * out_segment_length)
            if (pos - out_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'out'

        return None, None
    
    def evaluate_curve(self, time):
        """计算指定时间的曲线值"""
        if not self.keyframes:
            return 0.0
        
        # 找到时间范围内的关键帧
        if time <= self.keyframes[0].time:
            return self.keyframes[0].value
        if time >= self.keyframes[-1].time:
            return self.keyframes[-1].value
        
        # 找到相邻的两个关键帧
        for i in range(len(self.keyframes) - 1):
            kf1 = self.keyframes[i]
            kf2 = self.keyframes[i + 1]
            
            if kf1.time <= time <= kf2.time:
                # 检查是否为线性插值（两端切线都为零）
                if (kf1.out_tangent == (0.0, 0.0) and kf2.in_tangent == (0.0, 0.0)):
                    # 线性插值
                    t = (time - kf1.time) / (kf2.time - kf1.time)
                    return kf1.value + t * (kf2.value - kf1.value)
                else:
                    # 使用贝塞尔插值
                    t = (time - kf1.time) / (kf2.time - kf1.time)
                    
                    # 计算控制点，根据切线大小调整控制点距离
                    segment_length = kf2.time - kf1.time
                    
                    # 出切线控制点 - 根据切线大小调整
                    out_tangent_scale = segment_length * 0.3  # 基础比例
                    p1_x = kf1.time + kf1.out_tangent[0] * out_tangent_scale
                    p1_y = kf1.value + kf1.out_tangent[1] * out_tangent_scale
                    
                    # 入切线控制点 - 根据切线大小调整
                    in_tangent_scale = segment_length * 0.3  # 基础比例
                    p2_x = kf2.time + kf2.in_tangent[0] * in_tangent_scale
                    p2_y = kf2.value + kf2.in_tangent[1] * in_tangent_scale
                    
                    p0 = (kf1.time, kf1.value)
                    p1 = (p1_x, p1_y)
                    p2 = (p2_x, p2_y)
                    p3 = (kf2.time, kf2.value)
                    
                    # 三次贝塞尔曲线
                    value = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
                    return value
        
        return 0.0
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 绘制整个控件背景
        painter.fillRect(self.rect(), QtGui.QColor(30, 30, 30))

        # 曲线编辑区域
        rect = self.rect().adjusted(20, 20, -20, -20)

        # 绘制曲线区域背景（稍微亮一些，表示可编辑区域）
        painter.fillRect(rect, QtGui.QColor(40, 40, 40))
        painter.setPen(QtGui.QPen(QtGui.QColor(120, 120, 120), 2))
        painter.drawRect(rect)

        # 绘制网格
        if self.grid_enabled:
            self.draw_grid(painter, rect)

        # 绘制曲线
        self.draw_curve(painter)

        # 绘制关键帧
        self.draw_keyframes(painter)

        # 绘制区域提示文字和快捷键提示
        painter.setPen(QtGui.QPen(QtGui.QColor(150, 150, 150), 1))
        painter.setFont(QtGui.QFont("Arial", 9))
        painter.drawText(rect.left() + 5, rect.top() - 5, "曲线编辑区域")
    
    def draw_grid(self, painter, rect):
        """绘制网格"""
        painter.setPen(QtGui.QPen(QtGui.QColor(60, 60, 60), 1))
        
        # 垂直网格线
        for i in range(11):
            x = rect.left() + i * rect.width() / 10
            painter.drawLine(x, rect.top(), x, rect.bottom())
        
        # 水平网格线
        for i in range(11):
            y = rect.top() + i * rect.height() / 10
            painter.drawLine(rect.left(), y, rect.right(), y)

    def draw_curve(self, painter):
        """绘制贝塞尔曲线"""
        if len(self.keyframes) < 2:
            return

        painter.setPen(QtGui.QPen(QtGui.QColor(100, 200, 255), 2))

        # 绘制曲线段
        path = QtGui.QPainterPath()

        start_time = self.time_range[0]
        end_time = self.time_range[1]

        # 对于线性段，我们直接连接关键帧点
        # 对于贝塞尔段，我们使用更多的点来绘制平滑曲线

        # 首先，将关键帧按时间排序
        sorted_keyframes = sorted(self.keyframes, key=lambda k: k.time)

        # 检查是否所有段都是线性的
        all_linear = True
        for i in range(len(sorted_keyframes) - 1):
            kf1 = sorted_keyframes[i]
            kf2 = sorted_keyframes[i + 1]
            if not (kf1.out_tangent == (0.0, 0.0) and kf2.in_tangent == (0.0, 0.0)):
                all_linear = False
                break

        if all_linear:
            # 如果所有段都是线性的，直接连接关键帧点
            first_point = True
            for kf in sorted_keyframes:
                if kf.time < start_time or kf.time > end_time:
                    continue

                screen_pos = self.world_to_screen(kf.time, kf.value)

                if first_point:
                    path.moveTo(screen_pos)
                    first_point = False
                else:
                    path.lineTo(screen_pos)
        else:
            # 混合模式，使用更多的点来绘制曲线
            steps = 200

            first_point = True
            for i in range(steps + 1):
                t = start_time + (end_time - start_time) * i / steps
                value = self.evaluate_curve(t)
                screen_pos = self.world_to_screen(t, value)

                if first_point:
                    path.moveTo(screen_pos)
                    first_point = False
                else:
                    path.lineTo(screen_pos)

        painter.drawPath(path)

    def draw_keyframes(self, painter):
        """绘制关键帧和切线"""
        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)

            # 绘制切线
            if keyframe.selected or keyframe == self.selected_keyframe:
                painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 0), 1))

                # 计算切线长度比例 - 根据相邻关键帧距离调整
                segment_length = 30  # 默认长度

                # 如果有前一个关键帧，调整入切线长度
                prev_keyframe = None
                for kf in self.keyframes:
                    if kf.time < keyframe.time and (prev_keyframe is None or kf.time > prev_keyframe.time):
                        prev_keyframe = kf

                if prev_keyframe:
                    in_segment_length = (keyframe.time - prev_keyframe.time) * 30
                else:
                    in_segment_length = segment_length

                # 如果有后一个关键帧，调整出切线长度
                next_keyframe = None
                for kf in self.keyframes:
                    if kf.time > keyframe.time and (next_keyframe is None or kf.time < next_keyframe.time):
                        next_keyframe = kf

                if next_keyframe:
                    out_segment_length = (next_keyframe.time - keyframe.time) * 30
                else:
                    out_segment_length = segment_length

                # 入切线 - 长度根据切线值和段长度调整
                in_tangent_length = ((keyframe.in_tangent[0] ** 2 + keyframe.in_tangent[
                    1] ** 2) ** 0.5) * in_segment_length
                if in_tangent_length > 0:
                    in_tangent_dir = (keyframe.in_tangent[0] / in_tangent_length,
                                      keyframe.in_tangent[1] / in_tangent_length)
                    in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * in_segment_length,
                                                     -keyframe.in_tangent[1] * in_segment_length)
                    painter.drawLine(kf_pos, in_pos)
                    painter.fillRect(in_pos.x() - self.tangent_size // 2, in_pos.y() - self.tangent_size // 2,
                                     self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))

                # 出切线 - 长度根据切线值和段长度调整
                out_tangent_length = ((keyframe.out_tangent[0] ** 2 + keyframe.out_tangent[
                    1] ** 2) ** 0.5) * out_segment_length
                if out_tangent_length > 0:
                    out_tangent_dir = (keyframe.out_tangent[0] / out_tangent_length,
                                       keyframe.out_tangent[1] / out_tangent_length)
                    out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * out_segment_length,
                                                      -keyframe.out_tangent[1] * out_segment_length)
                    painter.drawLine(kf_pos, out_pos)
                    painter.fillRect(out_pos.x() - self.tangent_size // 2, out_pos.y() - self.tangent_size // 2,
                                     self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))
            
            painter.drawEllipse(kf_pos, self.keyframe_size, self.keyframe_size)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        # 获得焦点以接收键盘事件
        self.setFocus()

        # 将QPoint转换为QPointF
        pos = QtCore.QPointF(event.pos())

        if event.button() == QtCore.Qt.LeftButton:
            # 检查是否点击了切线控制点
            tangent_kf, tangent_type = self.get_tangent_at_pos(pos)
            if tangent_kf:
                self.selected_keyframe = tangent_kf  # 选中关键帧
                self.dragging_tangent = (tangent_kf, tangent_type)
                self.drag_start_pos = pos
                # 保存拖拽前状态
                self.drag_start_state = copy.deepcopy(self.keyframes)
                self.update()
                return

            # 检查是否点击了关键帧
            keyframe = self.get_keyframe_at_pos(pos)
            if keyframe:
                self.selected_keyframe = keyframe
                self.dragging_keyframe = keyframe
                self.drag_start_pos = pos
                # 保存拖拽前状态
                self.drag_start_state = copy.deepcopy(self.keyframes)
                print(f"选中关键帧: 时间={keyframe.time:.3f}, 值={keyframe.value:.3f} (按Delete键删除)")
            else:
                # 只有在曲线编辑区域内才添加新关键帧
                if self.is_pos_in_curve_area(pos):
                    time, value = self.screen_to_world(pos)
                    new_keyframe = self.add_keyframe(time, value)
                    self.selected_keyframe = new_keyframe  # 选中新创建的关键帧
                    print(f"创建关键帧: 时间={time:.3f}, 值={value:.3f}")
                else:
                    # 点击在区域外，取消选择
                    self.selected_keyframe = None
                    print("点击在编辑区域外，已取消选择")

            self.update()

        elif event.button() == QtCore.Qt.RightButton:
            # 删除关键帧（只在曲线区域内有效）
            if self.is_pos_in_curve_area(pos):
                keyframe = self.get_keyframe_at_pos(pos)
                if keyframe:
                    self.remove_keyframe(keyframe)
                    print(f"右键删除关键帧: 时间={keyframe.time:.3f}, 值={keyframe.value:.3f}")
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        # 将QPoint转换为QPointF
        pos = QtCore.QPointF(event.pos())

        if self.dragging_keyframe:
            # 拖拽关键帧 - 只有在曲线区域内才允许移动
            if self.is_pos_in_curve_area(pos):
                time, value = self.screen_to_world(pos)

                # 获取关键帧在列表中的索引
                keyframe_index = self.get_keyframe_index(self.dragging_keyframe)

                # 根据关键帧索引限制移动
                if keyframe_index == 0:
                    # 第一个关键帧：只能在最左边上下移动
                    self.dragging_keyframe.time = self.time_range[0]
                    self.dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], value))
                elif keyframe_index == 1:
                    # 第二个关键帧：只能在中间移动（时间固定在中点）
                    mid_time = (self.time_range[0] + self.time_range[1]) / 2
                    self.dragging_keyframe.time = mid_time
                    self.dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], value))
                elif keyframe_index == 2:
                    # 第三个关键帧：只能在最右边上下移动
                    self.dragging_keyframe.time = self.time_range[1]
                    self.dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], value))
                else:
                    # 其他关键帧：正常移动（如果有的话）
                    self.dragging_keyframe.time = max(self.time_range[0], min(self.time_range[1], time))
                    self.dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], value))

                self.keyframes.sort(key=lambda k: k.time)
                self.update()
                self.keyframe_changed.emit(self.keyframes)

        elif self.dragging_tangent:
            # 拖拽切线 - 切线可以延伸到区域外，但有合理限制
            keyframe, tangent_type = self.dragging_tangent
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)
            delta = pos - kf_pos

            # 计算切线长度比例 - 根据相邻关键帧距离调整
            segment_length = 30  # 默认长度
            
            # 如果有前一个关键帧，调整入切线长度
            prev_keyframe = None
            for kf in self.keyframes:
                if kf.time < keyframe.time and (prev_keyframe is None or kf.time > prev_keyframe.time):
                    prev_keyframe = kf
            
            if prev_keyframe and tangent_type == 'in':
                segment_length = (keyframe.time - prev_keyframe.time) * 30
            
            # 如果有后一个关键帧，调整出切线长度
            next_keyframe = None
            for kf in self.keyframes:
                if kf.time > keyframe.time and (next_keyframe is None or kf.time < next_keyframe.time):
                    next_keyframe = kf
                
            if next_keyframe and tangent_type == 'out':
                segment_length = (next_keyframe.time - keyframe.time) * 30

            # 限制切线长度，避免过度延伸
            max_tangent_length = segment_length * 2  # 最大长度为段长度的2倍
            if delta.manhattanLength() > max_tangent_length:
                # 归一化并限制长度
                length = (delta.x() ** 2 + delta.y() ** 2) ** 0.5
                if length > 0:
                    scale = max_tangent_length / length
                    delta = QtCore.QPointF(delta.x() * scale, delta.y() * scale)

            # 计算切线值 - 根据段长度归一化
            tangent_x = delta.x() / segment_length
            tangent_y = -delta.y() / segment_length

            if tangent_type == 'in':
                keyframe.in_tangent = (tangent_x, tangent_y)
            else:
                keyframe.out_tangent = (tangent_x, tangent_y)

            self.update()
            self.keyframe_changed.emit(self.keyframes)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        # 如果进行了拖拽操作，保存状态
        if self.dragging_keyframe or self.dragging_tangent:
            if hasattr(self, 'drag_start_state'):
                # 检查是否有实际改变
                if self.drag_start_state != self.keyframes:
                    if self.dragging_keyframe:
                        self.save_state(f"移动关键帧")
                    elif self.dragging_tangent:
                        self.save_state(f"调整切线")
                delattr(self, 'drag_start_state')

        self.dragging_keyframe = None
        self.dragging_tangent = None
        self.drag_start_pos = None

    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == QtCore.Qt.Key_Delete:
            # Delete键删除选中的关键帧
            self.delete_selected_keyframe()
        elif event.key() == QtCore.Qt.Key_Escape:
            # Escape键取消选择
            self.selected_keyframe = None
            self.update()
        elif event.key() == QtCore.Qt.Key_A and event.modifiers() == QtCore.Qt.ControlModifier:
            # Ctrl+A 添加关键帧到时间轴中间
            self.add_keyframe_at_center()
        elif event.key() == QtCore.Qt.Key_Z and event.modifiers() == QtCore.Qt.ControlModifier:
            # Ctrl+Z 撤销
            self.undo()
        elif event.key() == QtCore.Qt.Key_Y and event.modifiers() == QtCore.Qt.ControlModifier:
            # Ctrl+Y 重做
            self.redo()
        else:
            # 调用父类处理其他按键
            super(BezierCurveWidget, self).keyPressEvent(event)

    def delete_selected_keyframe(self):
        """删除选中的关键帧"""
        if self.selected_keyframe and len(self.keyframes) > 2:
            self.remove_keyframe(self.selected_keyframe)
            print(f"已删除关键帧: 时间={self.selected_keyframe.time:.3f}, 值={self.selected_keyframe.value:.3f}")
        elif self.selected_keyframe and len(self.keyframes) <= 2:
            print("至少需要保留2个关键帧，无法删除")
        else:
            print("没有选中的关键帧")

    def add_keyframe_at_center(self):
        """在时间轴中间添加关键帧"""
        mid_time = (self.time_range[0] + self.time_range[1]) / 2
        mid_value = (self.value_range[0] + self.value_range[1]) / 2
        self.add_keyframe(mid_time, mid_value)
        print(f"已添加关键帧: 时间={mid_time:.3f}, 值={mid_value:.3f}")

    def focusInEvent(self, event):
        """获得焦点事件"""
        super(BezierCurveWidget, self).focusInEvent(event)
        self.update()  # 重绘以显示焦点状态

    def focusOutEvent(self, event):
        """失去焦点事件"""
        super(BezierCurveWidget, self).focusOutEvent(event)
        self.update()  # 重绘以隐藏焦点状态


class KeyframePropertiesWidget(QtWidgets.QWidget):
    """关键帧属性编辑器"""

    keyframe_property_changed = QtCore.Signal()  # 属性改变信号

    def __init__(self, parent=None):
        super(KeyframePropertiesWidget, self).__init__(parent)
        self.current_keyframe = None
        self.curve_widget = None  # 引用曲线编辑器
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QFormLayout()

        # 时间输入
        self.time_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_spinbox.setRange(-999.0, 999.0)
        self.time_spinbox.setDecimals(3)
        self.time_spinbox.valueChanged.connect(self.on_time_changed)
        layout.addRow("时间:", self.time_spinbox)

        # 值输入
        self.value_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_spinbox.setRange(-999.0, 999.0)
        self.value_spinbox.setDecimals(3)
        self.value_spinbox.valueChanged.connect(self.on_value_changed)
        layout.addRow("值:", self.value_spinbox)

        # 入切线
        self.in_tangent_x = QtWidgets.QDoubleSpinBox()
        self.in_tangent_x.setRange(-10.0, 10.0)
        self.in_tangent_x.setDecimals(3)
        self.in_tangent_x.valueChanged.connect(self.on_in_tangent_changed)

        self.in_tangent_y = QtWidgets.QDoubleSpinBox()
        self.in_tangent_y.setRange(-10.0, 10.0)
        self.in_tangent_y.setDecimals(3)
        self.in_tangent_y.valueChanged.connect(self.on_in_tangent_changed)

        in_layout = QtWidgets.QHBoxLayout()
        in_layout.addWidget(QtWidgets.QLabel("X:"))
        in_layout.addWidget(self.in_tangent_x)
        in_layout.addWidget(QtWidgets.QLabel("Y:"))
        in_layout.addWidget(self.in_tangent_y)

        in_widget = QtWidgets.QWidget()
        in_widget.setLayout(in_layout)
        layout.addRow("入切线:", in_widget)

        # 出切线
        self.out_tangent_x = QtWidgets.QDoubleSpinBox()
        self.out_tangent_x.setRange(-10.0, 10.0)
        self.out_tangent_x.setDecimals(3)
        self.out_tangent_x.valueChanged.connect(self.on_out_tangent_changed)

        self.out_tangent_y = QtWidgets.QDoubleSpinBox()
        self.out_tangent_y.setRange(-10.0, 10.0)
        self.out_tangent_y.setDecimals(3)
        self.out_tangent_y.valueChanged.connect(self.on_out_tangent_changed)

        out_layout = QtWidgets.QHBoxLayout()
        out_layout.addWidget(QtWidgets.QLabel("X:"))
        out_layout.addWidget(self.out_tangent_x)
        out_layout.addWidget(QtWidgets.QLabel("Y:"))
        out_layout.addWidget(self.out_tangent_y)

        out_widget = QtWidgets.QWidget()
        out_widget.setLayout(out_layout)
        layout.addRow("出切线:", out_widget)

        self.setLayout(layout)
        self.setEnabled(False)

    def set_keyframe(self, keyframe):
        """设置当前编辑的关键帧"""
        self.current_keyframe = keyframe

        if keyframe:
            self.setEnabled(True)
            self.time_spinbox.blockSignals(True)
            self.value_spinbox.blockSignals(True)
            self.in_tangent_x.blockSignals(True)
            self.in_tangent_y.blockSignals(True)
            self.out_tangent_x.blockSignals(True)
            self.out_tangent_y.blockSignals(True)

            self.time_spinbox.setValue(keyframe.time)
            self.value_spinbox.setValue(keyframe.value)
            self.in_tangent_x.setValue(keyframe.in_tangent[0])
            self.in_tangent_y.setValue(keyframe.in_tangent[1])
            self.out_tangent_x.setValue(keyframe.out_tangent[0])
            self.out_tangent_y.setValue(keyframe.out_tangent[1])

            self.time_spinbox.blockSignals(False)
            self.value_spinbox.blockSignals(False)
            self.in_tangent_x.blockSignals(False)
            self.in_tangent_y.blockSignals(False)
            self.out_tangent_x.blockSignals(False)
            self.out_tangent_y.blockSignals(False)
        else:
            self.setEnabled(False)

    def on_time_changed(self):
        """时间改变"""
        if self.current_keyframe and self.curve_widget:
            old_time = self.current_keyframe.time
            new_time = self.time_spinbox.value()

            if old_time != new_time:
                # 保存状态
                self.curve_widget.save_state(f"修改时间 {old_time:.3f} -> {new_time:.3f}")

                self.current_keyframe.time = new_time
                # 重新排序关键帧
                self.curve_widget.keyframes.sort(key=lambda k: k.time)
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_value_changed(self):
        """值改变"""
        if self.current_keyframe and self.curve_widget:
            old_value = self.current_keyframe.value
            new_value = self.value_spinbox.value()

            if old_value != new_value:
                # 保存状态
                self.curve_widget.save_state(f"修改值 {old_value:.3f} -> {new_value:.3f}")

                self.current_keyframe.value = new_value
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_in_tangent_changed(self):
        """入切线改变"""
        if self.current_keyframe and self.curve_widget:
            old_tangent = self.current_keyframe.in_tangent
            new_tangent = (self.in_tangent_x.value(), self.in_tangent_y.value())

            if old_tangent != new_tangent:
                # 保存状态
                self.curve_widget.save_state(f"修改入切线")

                self.current_keyframe.in_tangent = new_tangent
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_out_tangent_changed(self):
        """出切线改变"""
        if self.current_keyframe and self.curve_widget:
            old_tangent = self.current_keyframe.out_tangent
            new_tangent = (self.out_tangent_x.value(), self.out_tangent_y.value())

            if old_tangent != new_tangent:
                # 保存状态
                self.curve_widget.save_state(f"修改出切线")

                self.current_keyframe.out_tangent = new_tangent
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def set_curve_widget(self, curve_widget):
        """设置曲线编辑器引用"""
        self.curve_widget = curve_widget


class BezierCurveEditor(QtWidgets.QWidget):
    """贝塞尔曲线K帧编辑器主窗口"""

    def __init__(self, parent=None):
        super(BezierCurveEditor, self).__init__(parent)
        self.setWindowTitle("贝塞尔曲线K帧编辑器")
        self.setMinimumSize(800, 600)
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """初始化UI"""
        main_layout = QtWidgets.QHBoxLayout()

        # 左侧：曲线编辑器
        left_widget = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout()

        # 工具栏
        toolbar = self.create_toolbar()
        left_layout.addWidget(toolbar)

        # 曲线编辑器
        self.curve_widget = BezierCurveWidget()
        left_layout.addWidget(self.curve_widget)

        # 时间轴控制
        time_control = self.create_time_control()
        left_layout.addWidget(time_control)

        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget, 3)

        # 右侧：属性面板
        right_widget = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout()

        # 关键帧属性
        properties_group = QtWidgets.QGroupBox("关键帧属性")
        self.properties_widget = KeyframePropertiesWidget()
        # 设置曲线编辑器引用
        self.properties_widget.set_curve_widget(self.curve_widget)
        properties_layout = QtWidgets.QVBoxLayout()
        properties_layout.addWidget(self.properties_widget)
        properties_group.setLayout(properties_layout)
        right_layout.addWidget(properties_group)

        # 曲线设置
        settings_group = self.create_settings_group()
        right_layout.addWidget(settings_group)

        right_layout.addStretch()
        right_widget.setLayout(right_layout)
        main_layout.addWidget(right_widget, 1)

        self.setLayout(main_layout)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        # 添加关键帧按钮
        self.add_keyframe_btn = QtWidgets.QPushButton("添加关键帧 (Ctrl+A)")
        self.add_keyframe_btn.setToolTip("在时间轴中间添加关键帧\n快捷键: Ctrl+A")
        self.add_keyframe_btn.clicked.connect(self.add_keyframe)
        layout.addWidget(self.add_keyframe_btn)

        # 删除关键帧按钮
        self.delete_keyframe_btn = QtWidgets.QPushButton("删除关键帧 (Del)")
        self.delete_keyframe_btn.setToolTip("删除选中的关键帧\n快捷键: Delete")
        self.delete_keyframe_btn.clicked.connect(self.delete_keyframe)
        layout.addWidget(self.delete_keyframe_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 撤销按钮
        self.undo_btn = QtWidgets.QPushButton("撤销 (Ctrl+Z)")
        self.undo_btn.setToolTip("撤销上一步操作\n快捷键: Ctrl+Z")
        self.undo_btn.clicked.connect(self.undo_action)
        layout.addWidget(self.undo_btn)

        # 重做按钮
        self.redo_btn = QtWidgets.QPushButton("重做 (Ctrl+Y)")
        self.redo_btn.setToolTip("重做上一步操作\n快捷键: Ctrl+Y")
        self.redo_btn.clicked.connect(self.redo_action)
        layout.addWidget(self.redo_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 网格开关
        self.grid_checkbox = QtWidgets.QCheckBox("显示网格")
        self.grid_checkbox.setChecked(True)
        self.grid_checkbox.setToolTip("显示/隐藏网格线")
        self.grid_checkbox.toggled.connect(self.toggle_grid)
        layout.addWidget(self.grid_checkbox)

        layout.addStretch()
        toolbar.setLayout(layout)
        return toolbar

    def create_time_control(self):
        """创建时间轴控制"""
        widget = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        layout.addWidget(QtWidgets.QLabel("时间范围:"))

        self.time_start_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_start_spinbox.setRange(-999.0, 999.0)
        self.time_start_spinbox.setValue(0.0)
        self.time_start_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_start_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.time_end_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_end_spinbox.setRange(-999.0, 999.0)
        self.time_end_spinbox.setValue(10.0)
        self.time_end_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_end_spinbox)

        layout.addWidget(QtWidgets.QLabel("|"))
        layout.addWidget(QtWidgets.QLabel("值范围:"))

        self.value_min_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_min_spinbox.setRange(-999.0, 999.0)
        self.value_min_spinbox.setValue(-1.0)
        self.value_min_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_min_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.value_max_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_max_spinbox.setRange(-999.0, 999.0)
        self.value_max_spinbox.setValue(1.0)
        self.value_max_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_max_spinbox)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_settings_group(self):
        """创建设置组"""
        group = QtWidgets.QGroupBox("曲线设置")
        layout = QtWidgets.QVBoxLayout()

        # 关键帧切线预设
        tangent_layout = QtWidgets.QHBoxLayout()
        tangent_layout.addWidget(QtWidgets.QLabel("切线预设:"))
        self.tangent_preset_combo = QtWidgets.QComboBox()
        self.tangent_preset_combo.addItems(["自由", "线性", "缓入", "缓出", "缓入缓出", "弹跳"])
        self.tangent_preset_combo.currentIndexChanged.connect(self.apply_tangent_preset)
        tangent_layout.addWidget(self.tangent_preset_combo)
        layout.addLayout(tangent_layout)

        # 应用到所有关键帧
        apply_all_btn = QtWidgets.QPushButton("应用到所有关键帧")
        apply_all_btn.clicked.connect(self.apply_preset_to_all_keyframes)
        layout.addWidget(apply_all_btn)

        # 重置曲线按钮
        reset_btn = QtWidgets.QPushButton("重置曲线")
        reset_btn.clicked.connect(self.reset_curve)
        layout.addWidget(reset_btn)

        group.setLayout(layout)
        return group

    def create_presets_group(self):
        """创建预设组"""
        group = QtWidgets.QGroupBox("预设曲线")
        layout = QtWidgets.QVBoxLayout()

        # 预设按钮
        presets = [
            ("线性", self.apply_linear_preset),
            ("缓入", self.apply_ease_in_preset),
            ("缓出", self.apply_ease_out_preset),
            ("缓入缓出", self.apply_ease_in_out_preset),
            ("弹跳", self.apply_bounce_preset),
            ("重置", self.reset_curve)
        ]

        for name, callback in presets:
            btn = QtWidgets.QPushButton(name)
            btn.clicked.connect(callback)
            layout.addWidget(btn)

        group.setLayout(layout)
        return group

    def connect_signals(self):
        """连接信号"""
        self.curve_widget.keyframe_changed.connect(self.on_keyframe_changed)
        self.properties_widget.keyframe_property_changed.connect(self.on_property_changed)

        # 设置快捷键
        self.setup_shortcuts()

    def on_property_changed(self):
        """属性改变回调"""
        # 属性编辑器已经更新了曲线，这里只需要发射信号
        self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)

    def setup_shortcuts(self):
        """设置快捷键"""
        # Delete键删除关键帧
        delete_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Delete, self)
        delete_shortcut.activated.connect(self.delete_keyframe)

        # Ctrl+A添加关键帧
        add_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+A"), self)
        add_shortcut.activated.connect(self.add_keyframe)

        # Esc取消选择
        esc_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence(QtCore.Qt.Key_Escape), self)
        esc_shortcut.activated.connect(self.clear_selection)

        # Ctrl+Z撤销（预留）
        undo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Undo, self)
        undo_shortcut.activated.connect(self.undo_action)

        # Ctrl+Y重做（预留）
        redo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Redo, self)
        redo_shortcut.activated.connect(self.redo_action)

    def clear_selection(self):
        """清除选择"""
        self.curve_widget.selected_keyframe = None
        self.curve_widget.update()
        self.properties_widget.set_keyframe(None)
        print("已取消选择")

    def undo_action(self):
        """撤销操作"""
        if self.curve_widget.undo():
            # 更新属性面板
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)

    def redo_action(self):
        """重做操作"""
        if self.curve_widget.redo():
            # 更新属性面板
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)

    def on_keyframe_changed(self, keyframes):
        """关键帧改变回调"""
        # 更新属性面板
        if self.curve_widget.selected_keyframe:
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)
        else:
            self.properties_widget.set_keyframe(None)

        # 更新曲线显示
        self.curve_widget.update()

    def add_keyframe(self):
        """添加关键帧"""
        # 在时间轴中间添加关键帧
        time_range = self.curve_widget.time_range
        mid_time = (time_range[0] + time_range[1]) / 2
        self.curve_widget.add_keyframe(mid_time, 0.0)

    def delete_keyframe(self):
        """删除选中的关键帧"""
        if self.curve_widget.selected_keyframe:
            self.curve_widget.remove_keyframe(self.curve_widget.selected_keyframe)

    def toggle_grid(self, enabled):
        """切换网格显示"""
        self.curve_widget.grid_enabled = enabled
        self.curve_widget.update()

    def update_time_range(self):
        """更新时间范围"""
        start = self.time_start_spinbox.value()
        end = self.time_end_spinbox.value()
        if start < end:
            self.curve_widget.time_range = (start, end)
            self.curve_widget.update()

    def update_value_range(self):
        """更新值范围"""
        min_val = self.value_min_spinbox.value()
        max_val = self.value_max_spinbox.value()
        if min_val < max_val:
            self.curve_widget.value_range = (min_val, max_val)
            self.curve_widget.update()

    def apply_linear_preset(self):
        """应用线性预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        # 设置线性切线
        for kf in self.curve_widget.keyframes:
            kf.in_tangent = (0.0, 0.0)
            kf.out_tangent = (0.0, 0.0)

    def apply_ease_in_preset(self):
        """应用缓入预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (3.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-1.0, 0.0)

    def apply_ease_out_preset(self):
        """应用缓出预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (1.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-3.0, 0.0)

    def apply_ease_in_out_preset(self):
        """应用缓入缓出预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (2.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-2.0, 0.0)

    def apply_bounce_preset(self):
        """应用弹跳预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(3.0, 1.2)
        self.curve_widget.add_keyframe(6.0, 0.8)
        self.curve_widget.add_keyframe(10.0, 1.0)

    def reset_curve(self):
        """重置曲线"""
        # 保存状态
        self.curve_widget.save_state("重置曲线")
        
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(5.0, 1.0)
        self.curve_widget.add_keyframe(10.0, 0.0)
        self.curve_widget.update()
        self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)

    def apply_tangent_preset(self):
        """应用切线预设到当前选中的关键帧"""
        if not self.curve_widget.selected_keyframe:
            return
        
        preset = self.tangent_preset_combo.currentText()
        keyframe = self.curve_widget.selected_keyframe
        
        # 保存状态
        self.curve_widget.save_state(f"应用切线预设: {preset}")
        
        if preset == "线性":
            # 确保精确为0.0，避免浮点误差
            keyframe.in_tangent = (0.0, 0.0)
            keyframe.out_tangent = (0.0, 0.0)
        elif preset == "缓入":
            keyframe.in_tangent = (-2.0, 0.0)
            keyframe.out_tangent = (1.0, 0.0)
        elif preset == "缓出":
            keyframe.in_tangent = (-1.0, 0.0)
            keyframe.out_tangent = (2.0, 0.0)
        elif preset == "缓入缓出":
            keyframe.in_tangent = (-2.0, 0.0)
            keyframe.out_tangent = (2.0, 0.0)
        elif preset == "弹跳":
            keyframe.in_tangent = (-1.5, -1.0)
            keyframe.out_tangent = (1.5, 1.0)
        
        # 更新属性面板和曲线
        self.properties_widget.set_keyframe(keyframe)
        self.curve_widget.update()
        self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)

    def apply_preset_to_all_keyframes(self):
        """将当前预设应用到所有关键帧"""
        if not self.curve_widget.keyframes:
            return
        
        preset = self.tangent_preset_combo.currentText()
        
        # 保存状态
        self.curve_widget.save_state(f"应用切线预设到所有关键帧: {preset}")
        
        for keyframe in self.curve_widget.keyframes:
            if preset == "线性":
                keyframe.in_tangent = (0.0, 0.0)
                keyframe.out_tangent = (0.0, 0.0)
            elif preset == "缓入":
                keyframe.in_tangent = (-2.0, 0.0)
                keyframe.out_tangent = (1.0, 0.0)
            elif preset == "缓出":
                keyframe.in_tangent = (-1.0, 0.0)
                keyframe.out_tangent = (2.0, 0.0)
            elif preset == "缓入缓出":
                keyframe.in_tangent = (-2.0, 0.0)
                keyframe.out_tangent = (2.0, 0.0)
            elif preset == "弹跳":
                keyframe.in_tangent = (-1.5, -1.0)
                keyframe.out_tangent = (1.5, 1.0)
        
        # 更新曲线
        self.curve_widget.update()
        self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)


# 演示应用
class BezierCurveApp(QtWidgets.QApplication):
    """贝塞尔曲线编辑器应用"""

    def __init__(self, sys_argv):
        super(BezierCurveApp, self).__init__(sys_argv)
        self.editor = BezierCurveEditor()
        self.editor.show()


if __name__ == '__main__':
    app = BezierCurveApp(sys.argv)
    sys.exit(app.exec_())
