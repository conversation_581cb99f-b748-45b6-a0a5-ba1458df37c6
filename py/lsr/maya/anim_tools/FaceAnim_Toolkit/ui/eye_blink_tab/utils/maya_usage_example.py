# -*- coding: utf-8 -*-

"""
Maya使用示例：展示如何在Maya中使用AnalysisEyeballRotation类
"""

from maya import cmds
from maya import OpenMaya
from lsr.maya.anim_tools.FaceAnim_Toolkit.ui.eye_blink_tab.utils.analysis_eyeball_rotation import AnalysisEyeballRotation


def get_object_matrices_from_timeline(object_name, start_frame=1, end_frame=100):
    """
    从Maya时间轴获取对象的矩阵数据
    
    Args:
        object_name (str): Maya对象名称
        start_frame (int): 起始帧
        end_frame (int): 结束帧
        
    Returns:
        dict: {frame: MMatrix} 帧和矩阵的字典
    """
    frame_matrix_dict = {}
    
    # 保存当前时间
    current_time = cmds.currentTime(query=True)
    
    try:
        for frame in range(start_frame, end_frame + 1):
            # 设置当前帧
            cmds.currentTime(frame)
            
            # 获取局部矩阵
            matrix_list = cmds.getAttr(f'{object_name}.matrix')

            # 转换为MMatrix (修正了矩阵创建方法)
            matrix = OpenMaya.MMatrix(matrix_list)
            
            frame_matrix_dict[frame] = matrix
            
    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)
    
    return frame_matrix_dict


def analyze_object_rotation(object_name, frame_spacing=2, angle_threshold=15, start_frame=1, end_frame=100):
    """
    分析Maya对象的旋转变化
    
    Args:
        object_name (str): Maya对象名称
        frame_spacing (int): 帧间隔
        angle_threshold (float): 角度阈值（度）
        start_frame (int): 起始帧
        end_frame (int): 结束帧
        
    Returns:
        dict: 分析结果标记
    """
    # 检查对象是否存在
    if not cmds.objExists(object_name):
        raise RuntimeError(f"对象 '{object_name}' 不存在")
    
    print(f"开始分析对象 '{object_name}' 的旋转变化...")
    print(f"帧范围: {start_frame} - {end_frame}")
    print(f"帧间隔: {frame_spacing}, 角度阈值: {angle_threshold}度")
    
    # 获取矩阵数据
    frame_matrix_dict = get_object_matrices_from_timeline(object_name, start_frame, end_frame)
    
    # 创建分析器
    analyzer = AnalysisEyeballRotation()
    analyzer.frame_matrix_dict = frame_matrix_dict
    analyzer.frame_spacing = frame_spacing
    analyzer.angle_threshold = angle_threshold
    
    # 执行分析
    result = analyzer.analyze_rotation_changes()
    
    # 打印摘要
    analyzer.print_analysis_summary()
    
    return result


def create_keyframes_from_analysis(object_name, analysis_result, attribute_name="visibility"):
    """
    根据分析结果在Maya中创建关键帧

    新逻辑说明：
    - 0 表示首次超过阈值（眨眼开始）
    - 1 表示恢复到阈值内（眨眼结束）

    Args:
        object_name (str): Maya对象名称
        analysis_result (dict): 分析结果
        attribute_name (str): 要设置关键帧的属性名
    """
    if not analysis_result:
        print("没有分析结果，跳过关键帧创建")
        return

    print(f"在对象 '{object_name}' 的 '{attribute_name}' 属性上创建关键帧...")
    print("标记含义: 0=首次超过阈值(眨眼开始), 1=恢复到阈值内(眨眼结束)")

    # 保存当前时间
    current_time = cmds.currentTime(query=True)

    try:
        for frame, value in sorted(analysis_result.items()):
            # 设置当前帧
            cmds.currentTime(frame)

            # 设置属性值
            cmds.setAttr(f'{object_name}.{attribute_name}', value)

            # 创建关键帧
            cmds.setKeyframe(f'{object_name}.{attribute_name}')

            marker_type = "眨眼开始" if value == 0 else "眨眼结束"
            print(f"  帧 {frame}: {attribute_name} = {value} ({marker_type})")

    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)

    print("关键帧创建完成")


def example_usage():
    """
    使用示例
    """
    # 示例：分析选中对象的旋转
    selected_objects = cmds.ls(selection=True)
    
    if not selected_objects:
        print("请先选择一个对象")
        return
    
    object_name = selected_objects[0]
    
    try:
        # 分析旋转变化
        result = analyze_object_rotation(
            object_name=object_name,
            frame_spacing=3,  # 3帧间隔
            angle_threshold=20,  # 20度阈值
            start_frame=1,
            end_frame=350
        )
        
        # 根据结果创建关键帧（可选）
        if result:
            # 创建一个locator来显示结果
            locator_name = f"{object_name}_rotation_analysis"
            if cmds.objExists(locator_name):
                cmds.delete(locator_name)
            
            locator = cmds.spaceLocator(name=locator_name)[0]
            create_keyframes_from_analysis(locator, result, "visibility")
            
            print(f"结果已保存到locator: {locator_name}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

example_usage()
