# -*- coding: utf-8 -*-

"""
Maya使用示例：展示如何在Maya中使用AnalysisEyeballRotation类
"""

from maya import cmds
from maya import OpenMaya
from lsr.maya.anim_tools.FaceAnim_Toolkit.ui.eye_blink_tab.utils.analysis_eyeball_rotation import AnalysisEyeballRotation


def get_object_matrices_from_timeline(object_name, start_frame=1, end_frame=100):
    """
    从Maya时间轴获取对象的矩阵数据
    
    Args:
        object_name (str): Maya对象名称
        start_frame (int): 起始帧
        end_frame (int): 结束帧
        
    Returns:
        dict: {frame: MMatrix} 帧和矩阵的字典
    """
    frame_matrix_dict = {}
    
    # 保存当前时间
    current_time = cmds.currentTime(query=True)
    
    try:
        for frame in range(start_frame, end_frame + 1):
            # 设置当前帧
            cmds.currentTime(frame)
            
            # 获取局部矩阵
            matrix_list = cmds.getAttr(f'{object_name}.matrix')

            # 转换为MMatrix (正确的矩阵创建方法)
            matrix = OpenMaya.MMatrix(matrix_list)
            
            frame_matrix_dict[frame] = matrix
            
    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)
    
    return frame_matrix_dict


def analyze_object_rotation(object_name, frame_spacing=2, angle_threshold=15, start_frame=1, end_frame=100):
    """
    分析Maya对象的旋转变化
    
    Args:
        object_name (str): Maya对象名称
        frame_spacing (int): 帧间隔
        angle_threshold (float): 角度阈值（度）
        start_frame (int): 起始帧
        end_frame (int): 结束帧
        
    Returns:
        dict: 分析结果标记
    """
    # 检查对象是否存在
    if not cmds.objExists(object_name):
        raise RuntimeError(f"对象 '{object_name}' 不存在")
    
    print(f"开始分析对象 '{object_name}' 的旋转变化...")
    print(f"帧范围: {start_frame} - {end_frame}")
    print(f"帧间隔: {frame_spacing}, 角度阈值: {angle_threshold}度")
    
    # 获取矩阵数据
    frame_matrix_dict = get_object_matrices_from_timeline(object_name, start_frame, end_frame)
    
    # 创建分析器
    analyzer = AnalysisEyeballRotation()
    analyzer.frame_matrix_dict = frame_matrix_dict
    analyzer.frame_spacing = frame_spacing
    analyzer.angle_threshold = angle_threshold
    
    # 执行分析
    result = analyzer.analyze_rotation_changes()
    
    # 打印摘要
    analyzer.print_analysis_summary()
    
    return result


def create_keyframes_from_analysis(object_name, analysis_result, attribute_name="visibility"):
    """
    根据分析结果在Maya中创建关键帧

    完整眨眼周期逻辑：
    - 0 表示眨眼开始或眨眼周期完成
    - 1 表示眨眼结束
    - 完整周期：0(开始) → 1(结束) → 0(周期完成)

    Args:
        object_name (str): Maya对象名称
        analysis_result (dict): 分析结果
        attribute_name (str): 要设置关键帧的属性名
    """
    if not analysis_result:
        print("没有分析结果，跳过关键帧创建")
        return

    print(f"在对象 '{object_name}' 的 '{attribute_name}' 属性上创建关键帧...")
    print("完整眨眼周期: 0(开始/周期完成) → 1(结束) → 0(周期完成)")

    # 保存当前时间
    current_time = cmds.currentTime(query=True)

    try:
        # 分析结果中的0和1的含义
        frames_sorted = sorted(analysis_result.items())

        for i, (frame, value) in enumerate(frames_sorted):
            # 设置当前帧
            cmds.currentTime(frame)

            # 设置属性值
            cmds.setAttr(f'{object_name}.{attribute_name}', value)

            # 创建关键帧
            cmds.setKeyframe(f'{object_name}.{attribute_name}')

            # 判断标记类型
            if value == 0:
                # 判断是眨眼开始还是周期完成
                if i > 0 and frames_sorted[i-1][1] == 1:
                    marker_type = "眨眼周期完成"
                else:
                    marker_type = "眨眼开始"
            else:
                marker_type = "眨眼结束"

            print(f"  帧 {frame}: {attribute_name} = {value} ({marker_type})")

    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)

    print("关键帧创建完成")


def create_blink_animation_keyframes(object_name, analysis_result, blink_attribute="scaleY"):
    """
    根据分析结果创建眨眼动画关键帧

    这个函数专门用于创建眨眼动画，会在眨眼标记之间插入中间帧

    Args:
        object_name (str): Maya对象名称（通常是眼皮对象）
        analysis_result (dict): 分析结果
        blink_attribute (str): 眨眼属性名（如scaleY, rotateX等）
    """
    if not analysis_result:
        print("没有分析结果，跳过眨眼动画创建")
        return

    print(f"为对象 '{object_name}' 创建眨眼动画...")
    print(f"使用属性: {blink_attribute}")

    # 保存当前时间
    current_time = cmds.currentTime(query=True)

    try:
        frames_sorted = sorted(analysis_result.items())

        for i, (frame, value) in enumerate(frames_sorted):
            cmds.currentTime(frame)

            if value == 0:
                # 判断是眨眼开始还是周期完成
                if i > 0 and frames_sorted[i-1][1] == 1:
                    # 眨眼周期完成 - 设置为正常状态
                    blink_value = 1.0  # 正常睁眼状态
                    marker_type = "眨眼周期完成(睁眼)"
                else:
                    # 眨眼开始 - 设置为闭眼状态
                    blink_value = 0.01  # 几乎完全闭眼
                    marker_type = "眨眼开始(闭眼)"
            else:
                # 眨眼结束 - 开始睁眼
                blink_value = 1.0  # 正常睁眼状态
                marker_type = "眨眼结束(睁眼)"

            # 设置属性值
            cmds.setAttr(f'{object_name}.{blink_attribute}', blink_value)

            # 创建关键帧
            cmds.setKeyframe(f'{object_name}.{blink_attribute}')

            print(f"  帧 {frame}: {blink_attribute} = {blink_value:.2f} ({marker_type})")

    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)

    print("眨眼动画关键帧创建完成")


def debug_analysis_result(analysis_result):
    """
    调试分析结果，帮助诊断问题
    """
    print("\n=== 调试分析结果 ===")
    if not analysis_result:
        print("❌ 分析结果为空！")
        print("可能的原因：")
        print("1. 选中的对象没有足够的旋转变化")
        print("2. 角度阈值设置过高")
        print("3. 帧间隔设置不合适")
        print("4. 动画数据获取失败")
        return False

    print(f"✅ 检测到 {len(analysis_result)} 个标记")

    # 分析标记模式
    frames_sorted = sorted(analysis_result.items())
    zeros = [f for f, v in frames_sorted if v == 0]
    ones = [f for f, v in frames_sorted if v == 1]

    print(f"0标记(眨眼开始/周期完成): {len(zeros)} 个 - 帧: {zeros}")
    print(f"1标记(眨眼结束): {len(ones)} 个 - 帧: {ones}")

    # 检查模式是否正确
    if len(zeros) == 0 and len(ones) == 0:
        print("❌ 没有检测到任何眨眼标记")
        return False
    elif len(ones) == 0:
        print("⚠️  只检测到0标记，没有1标记 - 可能眨眼没有完成")
    elif len(zeros) < len(ones):
        print("⚠️  0标记少于1标记 - 可能缺少眨眼开始标记")
    elif len(zeros) > len(ones) + 1:
        print("⚠️  0标记过多 - 可能有多余的周期完成标记")
    else:
        print("✅ 标记模式看起来正常")

    # 分析眨眼周期
    print("\n眨眼周期分析:")
    for i, (frame, value) in enumerate(frames_sorted):
        if value == 0:
            if i > 0 and frames_sorted[i-1][1] == 1:
                print(f"  帧 {frame}: 眨眼周期完成")
            else:
                print(f"  帧 {frame}: 眨眼开始")
        else:
            print(f"  帧 {frame}: 眨眼结束")

    return True


def example_usage():
    """
    使用示例
    """
    # 示例：分析选中对象的旋转
    selected_objects = cmds.ls(selection=True)
    
    if not selected_objects:
        print("请先选择一个对象")
        return
    
    object_name = selected_objects[0]
    
    try:
        # 分析旋转变化
        result = analyze_object_rotation(
            object_name=object_name,
            frame_spacing=4,  # 4帧间隔
            angle_threshold=20,  # 20度阈值
            start_frame=1,
            end_frame=150
        )
        
        # 调试分析结果
        debug_success = debug_analysis_result(result)

        # 根据结果创建关键帧（可选）
        if result and debug_success:
            # 方法1: 创建一个locator来显示分析结果
            locator_name = f"{object_name}_rotation_analysis"
            if cmds.objExists(locator_name):
                cmds.delete(locator_name)

            locator = cmds.spaceLocator(name=locator_name)[0]
            create_keyframes_from_analysis(locator, result, "visibility")

            print(f"分析结果已保存到locator: {locator_name}")

            # 方法2: 如果选中的是眼皮对象，直接创建眨眼动画
            print("\n选择操作:")
            print("1. 仅查看分析结果")
            print("2. 为选中对象创建眨眼动画")
            user_choice = input("请选择 (1/2): ").strip()

            if user_choice == '2':
                blink_attr = input("请输入眨眼属性名 (默认: scaleY): ").strip()
                if not blink_attr:
                    blink_attr = "scaleY"

                create_blink_animation_keyframes(object_name, result, blink_attr)
                print(f"眨眼动画已应用到对象: {object_name}")
        else:
            print("\n❌ 未检测到有效的眨眼动作")
            print("建议:")
            print("1. 检查选中对象是否有旋转动画")
            print("2. 调整angle_threshold参数（当前: 10度）")
            print("3. 调整frame_spacing参数（当前: 1帧）")
            print("4. 检查帧范围是否包含眨眼动画")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

example_usage()
