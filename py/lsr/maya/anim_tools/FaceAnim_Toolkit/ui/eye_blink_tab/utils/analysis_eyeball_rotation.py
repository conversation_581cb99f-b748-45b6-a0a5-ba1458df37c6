# -*- coding: utf-8 -*-

import math
from maya import cmds
from maya import OpenMaya


class AnalysisEyeballRotation(object):

    def __init__(self, *args, **kwargs):
        # 测试数据：添加一些有旋转变化的矩阵用于测试
        frame_matrix_dict = {
            1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
            2: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.866, -0.5, 0, 0, 0.5, 0.866, 0, 0, 0, 0, 1]),  # 30度旋转
            3: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.707, -0.707, 0, 0, 0.707, 0.707, 0, 0, 0, 0, 1]),  # 45度旋转
            4: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.5, -0.866, 0, 0, 0.866, 0.5, 0, 0, 0, 0, 1]),  # 60度旋转
            5: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1]),  # 90度旋转
            6: OpenMaya.MMatrix([1, 0, 0, 0, 0, -0.5, -0.866, 0, 0, 0.866, -0.5, 0, 0, 0, 0, 1])  # 120度旋转
        }
        frame_spacing = 2  # n帧间隔
        angle_threshold = 15  # 角度阈值（度）

        self.frame_matrix_dict = frame_matrix_dict
        self.frame_spacing = frame_spacing
        self.angle_threshold = angle_threshold

    def extract_rotation_from_matrix(self, matrix):
        """
        从MMatrix中提取旋转信息（欧拉角）

        Args:
            matrix (OpenMaya.MMatrix): 输入矩阵

        Returns:
            tuple: (rx, ry, rz) 旋转角度（度）
        """
        # 创建变换矩阵
        transform_matrix = OpenMaya.MTransformationMatrix(matrix)

        # 获取欧拉旋转
        euler_rotation = transform_matrix.eulerRotation()

        # 转换为度数
        rx = math.degrees(euler_rotation.x)
        ry = math.degrees(euler_rotation.y)
        rz = math.degrees(euler_rotation.z)

        return (rx, ry, rz)

    def calculate_rotation_difference(self, rotation1, rotation2):
        """
        计算两个旋转之间的角度差异

        Args:
            rotation1 (tuple): 第一个旋转 (rx, ry, rz)
            rotation2 (tuple): 第二个旋转 (rx, ry, rz)

        Returns:
            float: 总的旋转角度差异（度）
        """
        # 计算每个轴的角度差
        diff_x = rotation2[0] - rotation1[0]
        diff_y = rotation2[1] - rotation1[1]
        diff_z = rotation2[2] - rotation1[2]

        # 处理角度环绕问题（-180到180度）
        def normalize_angle(angle):
            while angle > 180:
                angle -= 360
            while angle < -180:
                angle += 360
            return angle

        diff_x = normalize_angle(diff_x)
        diff_y = normalize_angle(diff_y)
        diff_z = normalize_angle(diff_z)

        # 计算总的旋转幅度（欧几里得距离）
        total_rotation = math.sqrt(diff_x*diff_x + diff_y*diff_y + diff_z*diff_z)

        return total_rotation

    def analyze_blink_amplitude(self, start_frame, frames_to_analyze=10):
        """
        分析眨眼开始后指定帧数内的最大旋转幅度

        Args:
            start_frame (int): 眨眼开始帧
            frames_to_analyze (int): 要分析的帧数（默认10帧）

        Returns:
            float: 最大旋转幅度（度）
        """
        max_amplitude = 0.0
        base_frame = start_frame - self.frame_spacing

        # 确保基准帧存在
        if base_frame not in self.frame_matrix_dict:
            return max_amplitude

        base_matrix = self.frame_matrix_dict[base_frame]
        base_rotation = self.extract_rotation_from_matrix(base_matrix)

        # 分析后续帧的旋转幅度
        for i in range(frames_to_analyze):
            check_frame = start_frame + i
            if check_frame not in self.frame_matrix_dict:
                continue

            check_matrix = self.frame_matrix_dict[check_frame]
            check_rotation = self.extract_rotation_from_matrix(check_matrix)

            # 计算与基准帧的旋转差异
            rotation_diff = self.calculate_rotation_difference(base_rotation, check_rotation)
            max_amplitude = max(max_amplitude, rotation_diff)

        return max_amplitude

    def analyze_rotation_changes(self):
        """
        分析旋转变化并输出标记

        完整眨眼检测逻辑：
        - 如果旋转没有超过阈值，不输出任何内容
        - 如果首次出现超过阈值，输出0（眨眼开始）
        - 分析眨眼开始后10帧内的最大旋转幅度：
          * 如果 < 4.5倍阈值：小幅度眨眼 - 第1帧输出1，第3帧输出0
          * 如果 ≥ 4.5倍阈值：大幅度眨眼 - 第2帧输出1，第6帧输出0
        - 输出0后，等待2n帧才能再次检测新的眨眼
        - 在等待期间不输出任何内容

        Returns:
            dict: {frame: marker} 其中marker为0或1
                  0表示眨眼开始，1表示眨眼结束
        """
        result_markers = {}
        frames = sorted(self.frame_matrix_dict.keys())

        # 状态跟踪
        last_end_frame = None  # 上次输出0完成周期的帧号
        cooldown_frames = 2 * self.frame_spacing  # 冷却期：2n帧
        pending_blinks = {}  # 待处理的眨眼：{start_frame: amplitude}

        print("开始分析旋转变化...")
        print(f"帧间隔: {self.frame_spacing}, 角度阈值: {self.angle_threshold}度")
        print(f"眨眼幅度判断: <{4.5 * self.angle_threshold}度=小幅度眨眼, ≥{4.5 * self.angle_threshold}度=大幅度眨眼")
        print(f"眨眼结束后等待期: {cooldown_frames}帧")
        print(f"时间偏移修正: 所有标记向前偏移{self.frame_spacing}帧，与实际眼动时机同步")

        for current_frame in frames:
            # 计算对比帧（当前帧 - n帧）
            compare_frame = current_frame - self.frame_spacing

            # 检查对比帧是否存在
            if compare_frame not in self.frame_matrix_dict:
                continue

            # 处理待处理的眨眼标记
            blinks_to_remove = []
            for start_frame, amplitude in pending_blinks.items():
                frame_offset = current_frame - start_frame

                # 判断眨眼类型和输出时机
                amplitude_threshold = 4.5 * self.angle_threshold

                if amplitude < amplitude_threshold:
                    # 小幅度眨眼：第1帧输出1，第3帧输出0（考虑偏移）
                    if frame_offset == 1:
                        output_frame = current_frame - self.frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {output_frame}: 小幅度眨眼结束，标记1（偏移修正）")
                    elif frame_offset == 3:
                        output_frame = current_frame - self.frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {output_frame}: 小幅度眨眼周期完成，标记0（偏移修正），开始{cooldown_frames}帧等待期")
                        blinks_to_remove.append(start_frame)
                else:
                    # 大幅度眨眼：第2帧输出1，第6帧输出0（考虑偏移）
                    if frame_offset == 2:
                        output_frame = current_frame - self.frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {output_frame}: 大幅度眨眼结束，标记1（偏移修正）")
                    elif frame_offset == 6:
                        output_frame = current_frame - self.frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {output_frame}: 大幅度眨眼周期完成，标记0（偏移修正），开始{cooldown_frames}帧等待期")
                        blinks_to_remove.append(start_frame)

            # 移除已处理完的眨眼
            for start_frame in blinks_to_remove:
                del pending_blinks[start_frame]

            # 检查是否在冷却期内或有待处理的眨眼
            if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                if pending_blinks:
                    print(f"帧 {current_frame}: 有待处理的眨眼，跳过新检测")
                else:
                    print(f"帧 {current_frame}: 在等待期内，跳过检测")
                continue

            # 获取当前帧和对比帧的矩阵
            current_matrix = self.frame_matrix_dict[current_frame]
            compare_matrix = self.frame_matrix_dict[compare_frame]

            # 提取旋转信息
            current_rotation = self.extract_rotation_from_matrix(current_matrix)
            compare_rotation = self.extract_rotation_from_matrix(compare_matrix)

            # 计算旋转差异
            rotation_diff = self.calculate_rotation_difference(compare_rotation, current_rotation)

            print(f"帧 {compare_frame} -> 帧 {current_frame}: 旋转差异 = {rotation_diff:.2f}度")

            # 判断当前是否超过阈值
            current_exceeds = rotation_diff > self.angle_threshold

            if current_exceeds:
                # 检测到眨眼开始（考虑偏移）
                blink_start_frame = current_frame - self.frame_spacing
                result_markers[blink_start_frame] = 0

                # 分析眨眼幅度
                amplitude = self.analyze_blink_amplitude(current_frame, 10)
                pending_blinks[current_frame] = amplitude

                amplitude_threshold = 4.5 * self.angle_threshold
                blink_type = "小幅度" if amplitude < amplitude_threshold else "大幅度"
                print(f"  -> 眨眼开始! 在帧{blink_start_frame}标记0（偏移修正）")
                print(f"     眨眼幅度: {amplitude:.2f}度 ({blink_type}眨眼)")
            else:
                # 未超过阈值
                print(f"  -> 未超过阈值")

        return result_markers

    def get_analysis_result(self):
        """
        获取分析结果的便捷方法

        Returns:
            dict: 分析结果
        """
        return self.analyze_rotation_changes()

    def print_analysis_summary(self):
        """
        打印分析摘要
        """
        markers = self.analyze_rotation_changes()

        print("\n=== 分析摘要 ===")
        print(f"总帧数: {len(self.frame_matrix_dict)}")
        print(f"帧间隔: {self.frame_spacing}")
        print(f"角度阈值: {self.angle_threshold}度")
        print(f"检测到的标记: {len(markers)}")

        if markers:
            print("\n标记结果:")
            for frame in sorted(markers.keys()):
                marker_type = "首次超过阈值" if markers[frame] == 0 else "恢复到阈值内"
                print(f"  帧 {frame}: {markers[frame]} ({marker_type})")
        else:
            print("未检测到超过阈值的旋转变化")
