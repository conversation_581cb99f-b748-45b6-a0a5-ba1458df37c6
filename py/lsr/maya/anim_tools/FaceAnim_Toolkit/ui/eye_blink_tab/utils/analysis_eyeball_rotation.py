# -*- coding: utf-8 -*-

import math
from maya import cmds
from maya import OpenMaya


class AnalysisEyeballRotation(object):

    def __init__(self, *args, **kwargs):
        # 测试数据：添加一些有旋转变化的矩阵用于测试
        frame_matrix_dict = {
            1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
            2: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.866, -0.5, 0, 0, 0.5, 0.866, 0, 0, 0, 0, 1]),  # 30度旋转
            3: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.707, -0.707, 0, 0, 0.707, 0.707, 0, 0, 0, 0, 1]),  # 45度旋转
            4: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.5, -0.866, 0, 0, 0.866, 0.5, 0, 0, 0, 0, 1]),  # 60度旋转
            5: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1]),  # 90度旋转
            6: OpenMaya.MMatrix([1, 0, 0, 0, 0, -0.5, -0.866, 0, 0, 0.866, -0.5, 0, 0, 0, 0, 1])  # 120度旋转
        }
        frame_spacing = 2  # n帧间隔
        angle_threshold = 15  # 角度阈值（度）

        self.frame_matrix_dict = frame_matrix_dict
        self.frame_spacing = frame_spacing
        self.angle_threshold = angle_threshold

    def extract_rotation_from_matrix(self, matrix):
        """
        从MMatrix中提取旋转信息（欧拉角）

        Args:
            matrix (OpenMaya.MMatrix): 输入矩阵

        Returns:
            tuple: (rx, ry, rz) 旋转角度（度）
        """
        # 创建变换矩阵
        transform_matrix = OpenMaya.MTransformationMatrix(matrix)

        # 获取欧拉旋转
        euler_rotation = transform_matrix.eulerRotation()

        # 转换为度数
        rx = math.degrees(euler_rotation.x)
        ry = math.degrees(euler_rotation.y)
        rz = math.degrees(euler_rotation.z)

        return (rx, ry, rz)

    def calculate_rotation_difference(self, rotation1, rotation2):
        """
        计算两个旋转之间的角度差异

        Args:
            rotation1 (tuple): 第一个旋转 (rx, ry, rz)
            rotation2 (tuple): 第二个旋转 (rx, ry, rz)

        Returns:
            float: 总的旋转角度差异（度）
        """
        # 计算每个轴的角度差
        diff_x = rotation2[0] - rotation1[0]
        diff_y = rotation2[1] - rotation1[1]
        diff_z = rotation2[2] - rotation1[2]

        # 处理角度环绕问题（-180到180度）
        def normalize_angle(angle):
            while angle > 180:
                angle -= 360
            while angle < -180:
                angle += 360
            return angle

        diff_x = normalize_angle(diff_x)
        diff_y = normalize_angle(diff_y)
        diff_z = normalize_angle(diff_z)

        # 计算总的旋转幅度（欧几里得距离）
        total_rotation = math.sqrt(diff_x*diff_x + diff_y*diff_y + diff_z*diff_z)

        return total_rotation

    def analyze_rotation_changes(self):
        """
        分析旋转变化并输出标记

        完整眨眼检测逻辑：
        - 如果旋转没有超过阈值，不输出任何内容
        - 如果首次出现超过阈值，输出0（眨眼开始）
        - 从输出0到输出1之间，不输出任何内容
        - 当旋转恢复到阈值内时，输出1（眨眼结束）
        - 输出1之后，需要等待2n帧才能再次检测新的眨眼（输出0）
        - 在等待期间不输出任何内容

        Returns:
            dict: {frame: marker} 其中marker为0或1
                  0表示眨眼开始，1表示眨眼结束
        """
        result_markers = {}
        frames = sorted(self.frame_matrix_dict.keys())

        # 状态跟踪
        is_exceeding_threshold = False  # 是否超过阈值
        last_end_frame = None  # 上次输出1的帧号
        cooldown_frames = 2 * self.frame_spacing  # 冷却期：2n帧

        print("开始分析旋转变化...")
        print(f"帧间隔: {self.frame_spacing}, 角度阈值: {self.angle_threshold}度")
        print(f"眨眼结束后冷却期: {cooldown_frames}帧")

        for current_frame in frames:
            # 计算对比帧（当前帧 - n帧）
            compare_frame = current_frame - self.frame_spacing

            # 检查对比帧是否存在
            if compare_frame not in self.frame_matrix_dict:
                continue

            # 检查是否在冷却期内
            if last_end_frame is not None and current_frame - last_end_frame < cooldown_frames:
                print(f"帧 {current_frame}: 在冷却期内，跳过检测")
                continue

            # 获取当前帧和对比帧的矩阵
            current_matrix = self.frame_matrix_dict[current_frame]
            compare_matrix = self.frame_matrix_dict[compare_frame]

            # 提取旋转信息
            current_rotation = self.extract_rotation_from_matrix(current_matrix)
            compare_rotation = self.extract_rotation_from_matrix(compare_matrix)

            # 计算旋转差异
            rotation_diff = self.calculate_rotation_difference(compare_rotation, current_rotation)

            print(f"帧 {compare_frame} -> 帧 {current_frame}: 旋转差异 = {rotation_diff:.2f}度")

            # 判断当前是否超过阈值
            current_exceeds = rotation_diff > self.angle_threshold

            if current_exceeds and not is_exceeding_threshold:
                # 首次超过阈值，输出0（眨眼开始）
                result_markers[current_frame] = 0
                is_exceeding_threshold = True
                print(f"  -> 眨眼开始! 在帧{current_frame}标记0")
            elif not current_exceeds and is_exceeding_threshold:
                # 从超过阈值恢复到阈值内，输出1（眨眼结束）
                result_markers[current_frame] = 1
                is_exceeding_threshold = False
                last_end_frame = current_frame  # 记录眨眼结束帧，开始冷却期
                print(f"  -> 眨眼结束! 在帧{current_frame}标记1，开始{cooldown_frames}帧冷却期")
            elif current_exceeds and is_exceeding_threshold:
                # 仍然超过阈值，不输出（眨眼进行中）
                print(f"  -> 眨眼进行中，不输出标记")
            else:
                # 未超过阈值且之前也未超过，不输出
                print(f"  -> 未超过阈值")

        return result_markers

    def get_analysis_result(self):
        """
        获取分析结果的便捷方法

        Returns:
            dict: 分析结果
        """
        return self.analyze_rotation_changes()

    def print_analysis_summary(self):
        """
        打印分析摘要
        """
        markers = self.analyze_rotation_changes()

        print("\n=== 分析摘要 ===")
        print(f"总帧数: {len(self.frame_matrix_dict)}")
        print(f"帧间隔: {self.frame_spacing}")
        print(f"角度阈值: {self.angle_threshold}度")
        print(f"检测到的标记: {len(markers)}")

        if markers:
            print("\n标记结果:")
            for frame in sorted(markers.keys()):
                marker_type = "首次超过阈值" if markers[frame] == 0 else "恢复到阈值内"
                print(f"  帧 {frame}: {markers[frame]} ({marker_type})")
        else:
            print("未检测到超过阈值的旋转变化")
